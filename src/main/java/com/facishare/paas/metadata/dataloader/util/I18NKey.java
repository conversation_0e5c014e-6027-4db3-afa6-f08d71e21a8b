package com.facishare.paas.metadata.dataloader.util;

/**
 * <AUTHOR>
 * @date 2019-04-15 16:08
 */
public interface I18NKey {
    String ASSOCIATE_OBJECT_ID = "paas.udobj.associate_obj_id";
    String RELATED_MARK = "paas.udobj.related_mark";
    String IMPORT_RESULT = "paas.udobj.import_result";
    String IMPORT_SUCCESS = "paas.udobj.import_success";
    String IMPORT_FAIL = "paas.udobj.import_fail";
    String IMPORT_NO_IMPORT = "paas.udobj.import_no_import";
    String IMPORT_CANCEL = "paas.udobj.import_cancel";
    String IMPORT_FAIL_RESULT = "paas.udobj.import_fail_result";
    String IMPORT_ALL_RESULT = "paas.udobj.import_all_result";
    String IMPORT_UPLOAD_EXCEL = "paas.udobj.import_upload_excel";
    String IMPORT_NETWORK_ERROR = "paas.udobj.import_network_error";
    String IMPORT_ENCRYPT_NO_READ = "paas.udobj.import_encrypt_no_read";
    String IMPORT_ENCRYPT_OR_NOT_EXCEL_FILE = "paas.udobj.import_encrypt_or_not_excel_file";
    String IMPORT_SYSTEM_ERROR = "paas.udobj.import_system_error";
    String IMPORT_READ_FILE_FAILED = "paas.udobj.import_read_file_failed";
    String IMPORT_FILE_SIZE_EXCEED = "paas.udobj.import_file_size_exceed";
    String IMPORT_DATA_COUNT_EXCEED = "paas.udobj.import_data_count_exceed";
    String IMPORT_TRIGGER_APPROVAL_FLOW_EXCEED = "paas.udobj.import_trigger_approval_flow_exceed";
    String IMPORT_TRIGGER_WORK_FLOW_EXCEED = "paas.udobj.import_trigger_work_flow_exceed";
    String IMPORT_FILE_CONTENT_ERROR = "paas.udobj.import_file_content_error";
    String IMPORT_FILE_HEADER_IS_NULL = "paas.udobj.import_file_header_is_null";
    String IMPORT_FILE_HEADER_REPEAT_ERROR_MESSAGE = "paas.udobj.import_file_header_repeat_error_message";
    String IMPORT_FILE_EXT_ERROR_MESSAGE = "paas.udobj.import_file_ext_error_message";
    String IMPORT_COL_EXCEED = "paas.udobj.import_col_exceed";
    String IMPORT_DATA_TO_MONGO_FAILED = "paas.udobj.import_data_to_mongo_failed";
    String MUST_FILL_IN = "paas.udobj.must_fill_in";
    String EXPORT_EXCEED_LIMIT = "paas.udobj.export_exceed_limit";
    String IMPORT_MARK_EMPTY = "paas.udobj.import_mark_empty";
    String IMPORT_NULL_ERROR = "paas.udobj.import_null_error";
    //服务器繁忙
    String SYSTEM_ERROR = "paas.dataloader.system_error";
    //系统超时，请检查数据是否导入成功
    String SYSTEM_TIMEOUT_ERROR = "paas.dataloader.system_timeout_error";
}
