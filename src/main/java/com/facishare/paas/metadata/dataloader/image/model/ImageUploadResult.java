package com.facishare.paas.metadata.dataloader.image.model;

import lombok.Builder;
import lombok.Data;

/**
 * 图片上传结果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
public class ImageUploadResult {
    
    /**
     * 是否上传成功
     */
    @Builder.Default
    private boolean success = false;
    
    /**
     * 上传后的文件路径
     */
    private String uploadedPath;
    
    /**
     * 原始文件名
     */
    private String originalFileName;
    
    /**
     * 文件大小（字节）
     */
    @Builder.Default
    private long fileSize = 0;
    
    /**
     * 图片格式
     */
    private String format;
    
    /**
     * 上传耗时（毫秒）
     */
    @Builder.Default
    private long uploadDuration = 0;
    
    /**
     * 错误信息（上传失败时）
     */
    private String errorMessage;
    
    /**
     * 重试次数
     */
    @Builder.Default
    private int retryCount = 0;
    
    /**
     * 创建成功的上传结果
     * 
     * @param uploadedPath 上传后的文件路径
     * @param originalFileName 原始文件名
     * @param fileSize 文件大小
     * @return 成功的上传结果
     */
    public static ImageUploadResult success(String uploadedPath, String originalFileName, long fileSize) {
        return ImageUploadResult.builder()
            .success(true)
            .uploadedPath(uploadedPath)
            .originalFileName(originalFileName)
            .fileSize(fileSize)
            .build();
    }
    
    /**
     * 创建失败的上传结果
     * 
     * @param errorMessage 错误信息
     * @param originalFileName 原始文件名
     * @return 失败的上传结果
     */
    public static ImageUploadResult failure(String errorMessage, String originalFileName) {
        return ImageUploadResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .originalFileName(originalFileName)
            .build();
    }
    
    /**
     * 获取格式化的文件大小
     * 
     * @return 格式化的大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 获取上传速度（KB/s）
     * 
     * @return 上传速度
     */
    public double getUploadSpeed() {
        if (uploadDuration <= 0 || fileSize <= 0) {
            return 0.0;
        }
        
        return (fileSize / 1024.0) / (uploadDuration / 1000.0);
    }
    
    /**
     * 获取格式化的上传速度
     * 
     * @return 格式化的上传速度字符串
     */
    public String getFormattedUploadSpeed() {
        double speed = getUploadSpeed();
        if (speed < 1024) {
            return String.format("%.1f KB/s", speed);
        } else {
            return String.format("%.1f MB/s", speed / 1024.0);
        }
    }
    
    /**
     * 检查是否需要重试
     * 
     * @param maxRetries 最大重试次数
     * @return 如果需要重试返回true
     */
    public boolean shouldRetry(int maxRetries) {
        return !success && retryCount < maxRetries;
    }
    
    /**
     * 增加重试次数
     * 
     * @return 更新后的结果
     */
    public ImageUploadResult incrementRetry() {
        this.retryCount++;
        return this;
    }
    
    /**
     * 获取结果摘要
     * 
     * @return 结果摘要字符串
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("ImageUploadResult[");
        sb.append("success=").append(success);
        
        if (success) {
            sb.append(", path=").append(uploadedPath);
            sb.append(", size=").append(getFormattedFileSize());
            sb.append(", duration=").append(uploadDuration).append("ms");
            
            if (uploadDuration > 0) {
                sb.append(", speed=").append(getFormattedUploadSpeed());
            }
        } else {
            sb.append(", error=").append(errorMessage);
            
            if (retryCount > 0) {
                sb.append(", retries=").append(retryCount);
            }
        }
        
        if (originalFileName != null) {
            sb.append(", file=").append(originalFileName);
        }
        
        sb.append("]");
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}
