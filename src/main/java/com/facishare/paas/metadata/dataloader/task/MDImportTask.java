package com.facishare.paas.metadata.dataloader.task;

import com.facishare.paas.I18N;
import com.facishare.paas.metadata.dataloader.exception.DataLoaderBusinessException;
import com.facishare.paas.metadata.dataloader.exception.ExcelReadException;
import com.facishare.paas.metadata.dataloader.model.*;
import com.facishare.paas.metadata.dataloader.mongo.bean.ImportData;
import com.facishare.paas.metadata.dataloader.mongo.bean.QueryResult;
import com.facishare.paas.metadata.dataloader.mongo.dao.ImportDataDao;
import com.facishare.paas.metadata.dataloader.rest.dto.OldOwnerTeamMember;
import com.facishare.paas.metadata.dataloader.service.FileService;
import com.facishare.paas.metadata.dataloader.service.IBulkImportRestService;
import com.facishare.paas.metadata.dataloader.service.IImportTaskHookService;
import com.facishare.paas.metadata.dataloader.util.FileUtil;
import com.facishare.paas.metadata.dataloader.util.I18NExt;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import com.fxiaoke.limit.GuavaLimiter;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.dataloader.task.Job.*;

@Data
@Slf4j
@Builder
@SuppressWarnings("unchecked")
public class MDImportTask {
    private String excelFilePath;
    private String fileExt;
    private String locale;
    /**
     * 主对象apiName
     */
    private String importObjectApiName;
    /**
     * 主对象apiName
     */
    private String objectCode;
    /**
     * 主从导入的对象列表
     */
    @Builder.Default
    private List<String> unionImportApiNameList = Lists.newArrayList();
    private User user;
    private int importType;
    private int matchingType;
    private boolean isEmptyValueToUpdate;
    private String jobId;
    private String importCompleteUrl;
    private String pingUrl;
    private boolean isWorkFlowEnabled;
    private boolean isApprovalFlowEnabled;
    private boolean isTextureImportEnabled;
    boolean isUnionDuplicateChecking;
    private List<String> relatedApiNameList;    //支持销售记录导入关联对象
    private String specifiedField;
    private boolean checkOutOwner;
    private boolean removeOutTeamMember;
    private boolean updateOwner;
    private OldOwnerTeamMember oldOwnerTeamMember;
    private MasterInfo masterInfo;
    private List<DetailInfo> detailInfo;
    private boolean supportFieldMapping;
    private Map<String, Object> extendAttribute;
    private String importMethod;

    private FileService fileService;
    private IBulkImportRestService importRestService;

    private int doneCount;
    private int successCount;
    private int failCount;
    private int notImportCount;
    private int totalRowCount;
    //用户点击取消，取消任务
    private boolean isCancelTask;

    //系统出现异常，终止任务
    private boolean isAbortTask;

    private int batchHandleCount;
    private int batchTimeOut;
    @Builder.Default
    private int pageSize = 100;

    @Builder.Default
    List<DataItem> batchData = Lists.newArrayList();
    int batchCount;
    int masterDataCount;


    private Job job;
    private ExcelExecutor excelExecutor;
    private ImportDataDao importDataDao;
    @Builder.Default
    private List<String> detailApiNames = Lists.newArrayList();
    /**
     * 导入批次
     */
    private int importBatch;

    private FileUtil.LocalFile localFile;
    private IImportTaskHookService importTaskHookService;

    private static String appName = ConfigHelper.getProcessInfo().getName();
    private static String serverIp = ConfigHelper.getProcessInfo().getIp();
    private static String profile = ConfigHelper.getProcessInfo().getProfile();
    private static String action = "import";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Builder.Default
    private Map<String, List<DataItem>> errorDataMap = Maps.newHashMap();

    public MDImportTask init() {
        if (CollectionUtils.isNotEmpty(unionImportApiNameList)) {
            detailApiNames.addAll(unionImportApiNameList.stream().filter(x -> !Objects.equals(x, importObjectApiName)).collect(Collectors.toList()));
        }
        if (isSupportFieldMapping() && CollectionUtils.isNotEmpty(detailInfo)) {
            unionImportApiNameList = Lists.newArrayList();
            String apiName = masterInfo.getApiName();
            unionImportApiNameList.add(apiName);
            List<String> detailApiNames = detailInfo.stream().map(BaseInfo::getApiName).collect(Collectors.toList());
            unionImportApiNameList.addAll(detailApiNames);
        }
        excelExecutor = ExcelExecutor.builder()
                .fileExt(fileExt)
                .user(user)
                .unionImportApiNameList(unionImportApiNameList)
                .importRestService(importRestService)
                .supportFieldMapping(supportFieldMapping)
                .supportFieldMapping(supportFieldMapping)
                .importMethod(importMethod)
                .locale(locale)
                .build()
                .init();
        job = Job.builder()
                .jobId(jobId)
                .importCompleteUrl(importCompleteUrl)
                .pingUrl(pingUrl)
                .build()
                .init();
        return this;
    }

    private String execImportTaskHook(String path) {
        ImportTaskHook.Arg arg = ImportTaskHook.Arg.builder()
                .filePath(path)
                .importObjectApiName(importObjectApiName)
                .jobId(jobId)
                .fileExt(fileExt)
                .build();
        ImportTaskHook.Result result = importTaskHookService.before(user, arg);
        return result.getFilePath();
    }

    private String execImportTaskHookAfter(String filePath) {
        ImportTaskHook.Arg arg = ImportTaskHook.Arg.builder()
                .filePath(filePath)
                .importObjectApiName(importObjectApiName)
                .jobId(jobId)
                .fileExt(fileExt)
                .build();
        ImportTaskHook.Result result = importTaskHookService.after(user, arg);
        return result.getFilePath();
    }

    private void calcTotalRowCount(int curRow, List<String> rowList) {
        if (excelExecutor.isEmptyLine(rowList) || curRow == 1) {
            //空行或者标题行不计算总行数
            return;
        }
        totalRowCount++;
    }

    public void invoke() throws IOException {
        try {
            execute();
        } finally {
            //clean work
            job.stopHeartBeats();
            // 完成后删除本地文件
            Optional.ofNullable(localFile).ifPresent(FileUtil.LocalFile::deleteTempFile);
        }
    }

    private FileUtil.LocalFile getLocalFile() {
        if (Objects.nonNull(localFile)) {
            return localFile;
        }
        String newPath = execImportTaskHook(excelFilePath);
        if (StringUtils.isNotEmpty(newPath)) {
            log.warn("execImportTaskHook, oldPath:{}, newPath:{}", excelFilePath, newPath);
            excelFilePath = newPath;
        }
        return localFile = FileUtil.getInstance().saveFile(fileService, user, excelFilePath, fileExt);
    }

    public void execute() throws IOException {
        log.info("Start MDImportTask execute, user:{}, filePath:{}, fileExt:{}, apiName:{}, importCompleteUrl:{}, pingUrl:{} ",
                user, excelFilePath, fileExt, importObjectApiName, importCompleteUrl, pingUrl);
        String resultPath = null;
        try {
            //保存源excel文件到本地
            FileUtil.LocalFile localFile = getLocalFile();
            //创建心跳服务并定时调用
            createJob();
            excelExecutor.setImportPreProcessing(false);
            excelExecutor.setImportType(importType);
            excelExecutor.init();
            //执行导入
            executeImport(localFile.getPath());
            //上传结果
            resultPath = uploadExcel();
            //导入完成
            doComplete(resultPath);
        } catch (ExcelReadException e) {
            log.warn("import verify read excel fail, filePath:{}, jobId:{}", excelFilePath, jobId, e);
            importComplete(jobId, resultPath, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_READ_FILE_FAILED, "读取excel文件失败，请检查文件或者稍后重试"), "");//ignoreI18n
        } catch (Exception e) {
            log.error("Send complete by Unexpected Error, file token:{}, jobId:{} ", resultPath, jobId, e);
            if (excelExecutor.isExcelFormatWrong()) {
                importComplete(jobId, resultPath, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_CONTENT_ERROR, "Excel文件内容的格式不正确，请检查"), "");//ignoreI18n
            } else {
                importComplete(jobId, resultPath, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_FAIL, "失败"), "");//ignoreI18n
                throw e;
            }
        }
    }

    private String uploadExcel() throws IOException {
        String filePath = FileUtil.getInstance().uploadExcel(user, excelFilePath, fileExt, fileService, excelExecutor);
        String newPath = execImportTaskHookAfter(filePath);
        if (StringUtils.isNotEmpty(newPath)) {
            log.warn("execImportTaskHookAfter, oldPath:{}, newPath:{}", filePath, newPath);
            return newPath;
        }
        return filePath;
    }

    private void createJob() {
        job.create(() -> {
            return InvokeResult.builder()
                    .jobId(jobId)
                    .completeRowCount(doneCount)
                    .totalRowCount(totalRowCount)
                    .build();
        }, () -> {
            isCancelTask = true;
        }, () -> {
            isAbortTask = true;
            job.stopHeartBeats();
        });
    }

    private void executeImport(String path) throws IOException {
        //将数据读取到mongo
        excelExecutor.execute(path, (curRow, rowInfo) -> {
            calcTotalRowCount(curRow, rowInfo);
            if (curRow == 1 && CollectionUtils.isNotEmpty(unionImportApiNameList)) {
                //主从导入处理上一个sheet不足一批量的数据
                saveLastDataToMongo(getLastImportApiName());
                return true;
            } else if (curRow == 1) {
                return true;
            }
            try {
                batchSaveDataToMongo(curRow, rowInfo, getCurrentImportApiName());
            } catch (DataLoaderBusinessException e) {
                throw e;
            } catch (Exception e) {
                log.error("Error in handle row, rowIndex:{} ", curRow, e);
            }
            return true;
        });
        //处理最后不到一批次的数据
        saveLastDataToMongo(getCurrentImportApiName());
        //读取mongo数据,导入数据
        importDataFromMongo();
        updateResultExcel();
    }

    private void importDataFromMongo() {
        int maxQueryCount = masterDataCount % pageSize == 0 ? masterDataCount / pageSize : masterDataCount / pageSize + 1;
        int queryCount = 1;
        String lastId = null;
        boolean hasMore;
        do {
            if (queryCount > maxQueryCount) {
                break;
            }
            QueryResult result = importDataDao.findImportData(user, jobId, importObjectApiName, lastId, pageSize);
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getDataList())) {
                break;
            }
            handleImportData(result.getDataList(), !result.isHasMore());
            lastId = result.getLastId();
            hasMore = result.isHasMore();
            queryCount++;
        } while (StringUtils.isNotBlank(lastId) && hasMore);

    }

    private void handleImportData(List<ImportData> dataList, boolean finalBatch) {
        for (ImportData importData : dataList) {
            String importApiName = importData.getObjectApiName();
            String data = importData.getData();
            String masterId = importData.getMasterId();
            DataItem dataItem = DataItem.fromJsonString(data);
            MasterInfo master = new MasterInfo();
            master.setData(Lists.newArrayList(dataItem));
            master.setApiName(importApiName);
            if (Objects.nonNull(masterInfo) && CollectionUtils.isNotEmpty(masterInfo.getFieldMapping())) {
                master.setFieldMapping((masterInfo.getFieldMapping()));
            }
            List<DetailInfo> detailInfos = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(detailApiNames)) {
                for (String detailApiName : detailApiNames) {
                    importDataDao.findAndConsumer(user, jobId, detailApiName, masterId, pageSize, (detailDataList) -> {
                        if (!CollectionUtils.isEmpty(detailDataList)) {
                            DetailInfo detail = new DetailInfo();
                            detail.setApiName(detailApiName);
                            detail.setFieldMapping(CollectionUtils.emptyIfNull(detailInfo).stream()
                                    .filter(x -> Objects.equals(x.getApiName(), detailApiName))
                                    .findFirst()
                                    .map(DetailInfo::getFieldMapping)
                                    .orElse(Lists.newArrayList()));
                            List<DataItem> detailDataItemList = detailDataList.stream()
                                    .map(x -> DataItem.fromJsonString(x.getData()))
                                    .collect(Collectors.toList());
                            detail.setDataList(detailDataItemList);
                            Optional<DetailInfo> first = detailInfos.stream().filter(x -> Objects.equals(x.getApiName(), detailApiName)).findFirst();
                            if (first.isPresent()) {
                                first.get().getDataList().addAll(detailDataItemList);
                            } else {
                                detailInfos.add(detail);
                            }
                        }
                    });
                }
            }
            doImportData(importApiName, master, detailInfos, finalBatch);
        }
    }

    /**
     * 联合导入，导入上一个sheet不足一批量的数据
     * <p>
     * 联合导入解析多个sheet时，读取各个sheet的第1行时，表示此时处理的apiName已经更新了,
     * 这里是导入上一个sheet的剩余部分数据(不满pageSiz条)，所以先将当前处理的apiName设置为
     * 上一个sheet的apiName，处理完之后再还原.
     *
     * @param importApiName 对象apiName
     */
    private void saveLastDataToMongo(String importApiName) {
        if (CollectionUtils.isEmpty(batchData)) {
            return;
        }
        try {
            if (isAbortTask && !isCancelTask) {
                job.stopHeartBeats();
                excelExecutor.updateLastResultWhenCancel(batchData, importApiName);
            } else if (isCancelTask) {
                excelExecutor.updateLastResultWhenCancel(batchData, importApiName);
            } else {
                saveDataToMongo(importApiName);
            }
        } finally {
            batchData.clear();
        }
    }

    private void saveDataToMongo(String importApiName) {
        if (CollectionUtils.isEmpty(batchData)) {
            return;
        }
        try {
            List<ImportData> importDataList = Lists.newArrayList();
            List<DataItem> detailDataList = Lists.newArrayList();
            for (DataItem dataItem : batchData) {
                if (StringUtils.isBlank(dataItem.getMasterId())) {
                    errorDataMap.computeIfAbsent(importApiName, k -> Lists.newArrayList()).add(dataItem);
                    continue;
                }
                if (detailApiNames.contains(importApiName)) {
                    detailDataList.add(dataItem);
                } else {
                    importDataList.add(ImportData.builder()
                            .jobId(jobId)
                            .data(dataItem.toJsonString())
                            .objectApiName(importApiName)
                            .masterId(dataItem.getMasterId())
                            .build());
                }
            }
            if (CollectionUtils.isNotEmpty(detailDataList)) {
                List<String> detailMasterIds = detailDataList.stream().map(DataItem::getMasterId).collect(Collectors.toList());
                List<ImportData> masterDataList = importDataDao.findMasterData(user, jobId, importObjectApiName, detailMasterIds);
                List<String> masterIds = masterDataList.stream().map(ImportData::getMasterId).distinct().collect(Collectors.toList());
                for (DataItem detailData : detailDataList) {
                    if (masterIds.contains(detailData.getMasterId())) {
                        importDataList.add(ImportData.builder()
                                .jobId(jobId)
                                .data(detailData.toJsonString())
                                .objectApiName(importApiName)
                                .masterId(detailData.getMasterId())
                                .build());
                    } else {
                        errorDataMap.computeIfAbsent(importApiName, k -> Lists.newArrayList()).add(detailData);
                    }
                }
            }
            importDataDao.saveImportDataList(user, importDataList);
        } catch (Exception e) {
            log.error("Error in save data to mongo, importApiName:{}, batchData:{}", importApiName, batchData, e);
            throw new DataLoaderBusinessException(I18NExt.getOrDefault(I18NKey.IMPORT_DATA_TO_MONGO_FAILED, "导入保存数据异常，请重新重试"));//ignoreI18n
        } finally {
            if (Objects.equals(importApiName, importObjectApiName)) {
                masterDataCount += batchData.size();
            }
            batchData.clear();
        }
    }

    private void doComplete(String resultPath) {
        if (isAbortTask && !isCancelTask) {
            log.info("Send complete by abort, file token:{}, jobId:{} ", resultPath, jobId);
            importComplete(jobId, resultPath, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_FAIL, "失败"), "");//ignoreI18n
        } else if (isCancelTask) {
            log.info("Send complete by cancelling, file token:{}, jobId:{} ", resultPath, jobId);
            importComplete(jobId, resultPath, COMPLETE_CANCEL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_CANCEL, "取消"), "");//ignoreI18n
        } else {
            log.info("Send complete by Success, file token:{}, jobId:{} ", resultPath, jobId);
            String fileName = I18N.text(I18NKey.IMPORT_RESULT, formatter.format(LocalDate.now()));
            importComplete(jobId, resultPath, COMPLETE_SUCCESS_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_SUCCESS, "成功"), fileName);//ignoreI18n
        }
        // 使用biz-log-client包发送日志
        sendAuditLog();
    }

    /**
     * 发送日志
     */
    private void sendAuditLog() {
        AuditLogDTO dto = AuditLogDTO.builder()
                .appName(appName)
                .serverIp(serverIp)
                .profile(profile)
                .action(action)
                .ea(user.getEa())
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .objectApiNames(importObjectApiName)
                .num(doneCount)
                .build();
        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());

    }

    private void batchSaveDataToMongo(int curRow, List<String> rowInfo, String currentApiName) {
        if (isAbortTask && !isCancelTask) {
            job.stopHeartBeats();
            updateResultWhenCancel(rowInfo, currentApiName);
        } else if (isCancelTask) {
            updateResultWhenCancel(rowInfo, currentApiName);
        } else {
            if (batchCount > 0 && batchCount % pageSize == 0) {
                saveDataToMongo(currentApiName);
            }
            excelExecutor.toObjectData(rowInfo, curRow, true).ifPresent(data -> {
                batchCount++;
                batchData.add(data);
            });
        }
    }

    private String getCurrentImportApiName() {
        return excelExecutor.getCurrentApiName();
    }

    private String getLastImportApiName() {
        return excelExecutor.getLastApiName();
    }

    private void doImportData(String importApiName, MasterInfo masterInfo, List<DetailInfo> detailInfo, boolean finalBatch) {
        // 在这里限速
        waitForImport(masterInfo);
        int count = importBatch++;
        String traceId = TraceContext.get().getTraceId();
        try {
            TraceContext.get().setTraceId(String.format("%s_%s_%s", traceId, "data", count));
            IBulkImportRestService.BulkInsertArg bulkInsertArg = IBulkImportRestService.BulkInsertArg.builder()
                    .apiName(importApiName)
                    .importType(importType)
                    .matchingType(matchingType)
                    .isEmptyValueToUpdate(isEmptyValueToUpdate)
                    .isWorkFlowEnabled(isWorkFlowEnabled)
                    .isApprovalFlowEnabled(isApprovalFlowEnabled)
                    .isTextureImportEnabled(isTextureImportEnabled)
                    .rows(batchData)
                    .user(user)
                    .unionApiNames(unionImportApiNameList)
                    .isUnionDuplicateChecking(isUnionDuplicateChecking)
                    .jobId(jobId)
                    .relatedApiNameList(relatedApiNameList)
                    .objectCode(objectCode)
                    .specifiedField(specifiedField)
                    .checkOutOwner(checkOutOwner)
                    .removeOutTeamMember(removeOutTeamMember)
                    .finalBatch(finalBatch)
                    .updateOwner(updateOwner)
                    .oldOwnerTeamMember(oldOwnerTeamMember)
                    .fileCode(IBulkImportRestService.BulkInsertArg.FileCode.of(localFile.getMd5Code(), localFile.getSha256Code()))
                    .supportFieldMapping(supportFieldMapping)
                    .masterInfo(masterInfo)
                    .detailInfo(detailInfo)
                    .headerExcelCols(Lists.newArrayList())
                    .extendAttribute(extendAttribute)
                    .parameter(IBulkImportRestService.BulkInsertArg.Parameter.builder()
                            .importType(importType)
                            .matchingType(matchingType)
                            .isEmptyValueToUpdate(isEmptyValueToUpdate)
                            .isVerify(false)
                            .isWorkFlowEnabled(isWorkFlowEnabled)
                            .isApprovalFlowEnabled(isApprovalFlowEnabled)
                            .operationType(getOperationType())
                            .isUnionDuplicateChecking(isUnionDuplicateChecking)
                            .unionApiNames(unionImportApiNameList)
                            .jobId(jobId)
                            .relatedApiNameList(relatedApiNameList)
                            .objectCode(objectCode)
                            .specifiedField(specifiedField)
                            .checkOutOwner(checkOutOwner)
                            .removeOutTeamMember(removeOutTeamMember)
                            .updateOwner(updateOwner)
                            .oldOwnerTeamMember(oldOwnerTeamMember)
                            .importMethod(importMethod)
                            .build())
                    .locale(locale)
                    .build();
            IBulkImportRestService.BulkInsertResult result = importRestService.bulkInsert(bulkInsertArg, batchTimeOut);
            log.info("Bulk Import data current Line:{}, importCount:{} done", doneCount, batchData.size());
            if (null == result) {
                log.warn("Bulk Import data current Line:{}, importCount:{} done, but crm return null", doneCount, batchData.size());
            }
            //更新主对象结果
            updateResultExcel(masterInfo.getData(), result, masterInfo.getApiName());
            //更新从对象结果
            if (CollectionUtils.isNotEmpty(detailInfo)) {
                for (DetailInfo info : detailInfo) {
                    updateResultExcel(info.getDataList(), result, info.getApiName());
                }
            }
        } catch (DataLoaderBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error in bulkInsert batch data or update excel, current Line:{}, importCount:{},jobId:{}",
                    doneCount, getImportDataCount(masterInfo, detailInfo), jobId, e);
        } finally {
            //不论本批数据导入是否有问题，不影响下次处理
            log.info("Bulk Import data current Line:{}, importCount:{}, update Result Excel done", doneCount, batchData.size());
            doneCount += getImportDataCount(masterInfo, detailInfo);
            TraceContext.get().setTraceId(traceId);
        }
    }

    private static int getImportDataCount(MasterInfo masterInfo, List<DetailInfo> detailInfo) {
        return CollectionUtils.emptyIfNull(masterInfo.getData()).size() + CollectionUtils.emptyIfNull(detailInfo).stream().mapToInt(x -> x.getDataList().size()).sum();
    }


    private String getOperationType() {
        return OperationType.ADD_INVOKE.name();
    }

    private void waitForImport(MasterInfo masterInfo) {
        // doneCount 数量为 0 时(第一批导入时)立即执行导入，否则等待一段时间
        if (doneCount == 0) {
            return;
        }
        if (Objects.nonNull(masterInfo) && CollectionUtils.isEmpty(masterInfo.getData())) {
            return;
        }
        GuavaLimiter.acquire("limit-crm-md-import-dataloader-rate", user.getTenantId(), 60);
    }

    private void updateResultExcel() {
        if (MapUtils.isEmpty(errorDataMap)) {
            return;
        }
        for (Map.Entry<String, List<DataItem>> entry : errorDataMap.entrySet()) {
            String objectApiName = entry.getKey();
            List<DataItem> errorDataList = entry.getValue();
            if (CollectionUtils.isEmpty(errorDataList)) {
                continue;
            }
            IBulkImportRestService.BulkInsertResult result = IBulkImportRestService.BulkInsertResult.builder()
                    .success(false)
                    .message(I18NExt.getOrDefault(I18NKey.IMPORT_MARK_EMPTY, "关联标识为空，无法导入。"))//ignoreI18n
                    .build();
            result.setSuccess(false);
            updateResultExcel(errorDataList, result, objectApiName);
        }
    }


    private void updateResultExcel(List<DataItem> dataList, IBulkImportRestService.BulkInsertResult result, String importApiName) {
        ExcelExecutor.UpdateExcelResult updateExcelResult = excelExecutor.updateUnionResult(dataList, result, importApiName);
        successCount += updateExcelResult.getSuccessCount();
        failCount += updateExcelResult.getFailCount();
    }

    private void updateResultWhenCancel(List<String> rowInfo, String apiName) {
        if (excelExecutor.isEmptyLine(rowInfo)) {
            //空行，跳过
            return;
        }
        //联合导入
        excelExecutor.copyDataToUnionFailRow(rowInfo, apiName);
    }

    /**
     * 导入完成调用平台接口，包括失败和成功
     *
     * @param status 0 代表成功，-1 代码失败
     */
    private void importComplete(String jobId, String filePath, String status, String message, String fileName) {
        InvokeResult invokeResult = InvokeResult.builder()
                .jobId(jobId)
                .result(filePath)
                .code(status)
                .message(message)
                .successRowCount(successCount)
                .failRowCount(failCount)
                .notImportCount(notImportCount)
                .totalRowCount(COMPLETE_CANCEL_CODE.equals(status) ? successCount + failCount : totalRowCount)
                .fileExt(fileExt)
                .fileName(fileName)
                .importType(importType)
                .importObjectApiName(importObjectApiName)
                .unionImportApiNameList(unionImportApiNameList)
                .approvalFlowEnabled(isApprovalFlowEnabled)
                .workFlowEnabled(isWorkFlowEnabled)
                .user(user)
                .fileExpiredTime(FileService.getFileExpiredTimeWithNow())
                .build();
        job.completeJob(invokeResult);
        log.info("importComplete, ei:{}, jobId:{}", user.getTenantId(), jobId);
    }
}
