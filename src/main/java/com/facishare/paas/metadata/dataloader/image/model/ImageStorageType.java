package com.facishare.paas.metadata.dataloader.image.model;

/**
 * Excel图片存储类型枚举
 * 用于区分不同软件和版本的图片存储机制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ImageStorageType {
    
    /**
     * 标准OOXML格式（Microsoft Office / 新版WPS）
     * 使用drawing.xml + xl/media/的标准结构
     */
    OFFICE_STANDARD("Standard OOXML format"),
    
    /**
     * WPS DISPIMG函数格式
     * 使用WPS特有的DISPIMG函数引用图片
     */
    WPS_DISPIMG("WPS DISPIMG function format"),
    
    /**
     * WPS早期版本格式
     * WPS早期版本的特殊图片存储方式
     */
    WPS_LEGACY("WPS legacy format"),
    
    /**
     * 单元格内嵌图片（Excel 365新功能）
     * 图片直接嵌入到单元格中
     */
    EMBEDDED_CELL("Cell embedded image format");
    
    private final String description;
    
    ImageStorageType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return name() + " (" + description + ")";
    }
}
