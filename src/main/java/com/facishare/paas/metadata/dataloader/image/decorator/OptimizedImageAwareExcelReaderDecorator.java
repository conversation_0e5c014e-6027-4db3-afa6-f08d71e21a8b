package com.facishare.paas.metadata.dataloader.image.decorator;

import com.facishare.paas.metadata.dataloader.excel.IExcelReader;
import com.facishare.paas.metadata.dataloader.image.extractor.UnifiedImageExtractor;
import com.facishare.paas.metadata.dataloader.image.service.ImageUploadService;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.service.CrmMetadataService;
import com.facishare.paas.metadata.dataloader.util.IRowReader;
import com.facishare.paas.metadata.dataloader.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

/**
 * 优化的图片感知Excel读取器装饰器
 * 简化的装饰器实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class OptimizedImageAwareExcelReaderDecorator implements IExcelReader {

    private final IExcelReader delegate;
    private final List<String> apiNameList;
    private final User user;

    // 保存原始的RowReader，在process时使用
    private IRowReader originalRowReader;

    // 保存包装后的RowReader，用于获取统计信息
    private OptimizedImageAwareRowReader wrappedRowReader;

    /**
     * 构造函数
     *
     * @param delegate 被装饰的Excel读取器
     * @param apiNameList API名称列表
     * @param user 用户信息
     */
    public OptimizedImageAwareExcelReaderDecorator(IExcelReader delegate,
                                                   List<String> apiNameList, User user) {
        this.delegate = delegate;
        this.apiNameList = apiNameList;
        this.user = user;
    }

    @Override
    public void process(String fileName) throws IOException {
        log.info("Starting image-aware Excel processing for file: {}", fileName);

        try {
            // 创建包装的RowReader，在readRow中处理表头和图片
            UnifiedImageExtractor imageExtractor = SpringContextUtil.getBean(UnifiedImageExtractor.class);
            ImageUploadService imageUploadService = SpringContextUtil.getBean(ImageUploadService.class);
            CrmMetadataService crmMetadataService = SpringContextUtil.getBean(CrmMetadataService.class);
            this.wrappedRowReader = OptimizedImageAwareRowReader.builder()
                    .imageExtractor(imageExtractor)
                    .originalRowReader(originalRowReader)
                    .imageUploadService(imageUploadService)
                    .crmMetadataService(crmMetadataService)
                    .fileName(fileName)
                    .apiNameList(apiNameList)
                    .user(user)
                    .build();

            // 设置包装后的RowReader
            delegate.setRowReader(wrappedRowReader);

            // 执行Excel处理
            delegate.process(fileName);

        } catch (Exception e) {
            log.error("Error during image processing: {}", e.getMessage(), e);
            throw new IOException("Image processing failed", e);
        }
    }

    // IExcelReader接口实现

    @Override
    public void setRowReader(IRowReader reader) {
        // 暂存原始的RowReader，在process时进行包装
        this.originalRowReader = reader;
    }
}
