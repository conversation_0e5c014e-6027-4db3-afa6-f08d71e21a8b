package com.facishare.paas.metadata.dataloader.common;

import com.alibaba.fastjson.JSON;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2021/07/23
 */

@Getter
@Slf4j
public enum DataLoaderConfig {
    INSTANCE(),
    ;

    DataLoaderConfig() {
        ConfigFactory.getConfig("fs-paas-metadata-dataloader", iConfig -> {
            this.maxTryTimesFromConfig = iConfig.getInt("maxRetryTimes", 1800);
            this.intervalFromConfig = iConfig.getInt("interval", 4000);
            this.maxFinishedCount = iConfig.getInt("maxFinishedCount", 10);
            exportMaxCount = iConfig.getInt("exportMaxCount", 10000);
            dataBatchSize = iConfig.getInt("dataBatchSize", 200);
            bizBatchSize = parseMapFromConfig(iConfig, "bizBatchSize");
            excelColumnWidth = iConfig.getInt("excelColumnWidth", 20);
            importDataExpireTimeOffset = iConfig.getInt("importDataExpireTimeOffset", 0);
        });
    }

    private volatile int maxTryTimesFromConfig = 1800;
    private volatile int intervalFromConfig = 4000;
    private volatile int maxFinishedCount = 10;
    public volatile int exportMaxCount = 10000;
    public volatile int dataBatchSize = 200;
    public volatile int excelColumnWidth = 20;
    public int importDataExpireTimeOffset = 0;
    public volatile Map<String, Integer> bizBatchSize = Maps.newConcurrentMap();

    private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
        String data = config.get(key);
        if (Strings.isNullOrEmpty(data)) {
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
        try {
            Map map = JSON.parseObject(data, Map.class);
            return Collections.unmodifiableMap(map);
        } catch (Exception e) {
            log.error("parseMapFromConfig failed,config:{},key:{},data:{}", config.getName(), key, data, e);
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
    }

    public int getExportBizBatchSize(String biz) {
        return bizBatchSize.getOrDefault(biz, 200);
    }
}
